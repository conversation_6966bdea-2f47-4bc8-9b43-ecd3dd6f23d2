/**
 * Optimized Library Hook
 * 
 * Custom hook that provides optimized state management for the library page,
 * including debounced operations, performance monitoring, and efficient caching.
 */

import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useLibrary, useLibraryCollections } from "@renderer/hooks";
import { 
  optimizedFilterAndSort, 
  createDebouncedFunction,
  clearLibraryCache 
} from "@renderer/utils/optimized-library-utils";
import { 
  LIBRARY_PERFORMANCE_CONFIG, 
  libraryPerformanceMonitor 
} from "@renderer/config/library-performance";
import type { LibraryGame, LibraryFilters, LibrarySortBy } from "@types";

export interface OptimizedLibraryState {
  // Core data
  library: LibraryGame[];
  filteredAndSortedGames: LibraryGame[];
  
  // Loading states
  isLoading: boolean;
  isFilteringInProgress: boolean;
  
  // Performance metrics
  lastFilterTime: number;
  lastSortTime: number;
  
  // Optimized handlers
  handleSearchChange: (query: string) => void;
  handleFilterChange: (filters: Partial<LibraryFilters>) => void;
  handleSortChange: (sortBy: LibrarySortBy) => void;
  handleCollectionChange: (collectionId: string | null, source?: string) => void;
  handleClearFilters: () => void;
  
  // Performance utilities
  clearCache: () => void;
  getPerformanceMetrics: () => Record<string, any>;
}

export function useOptimizedLibrary(): OptimizedLibraryState {
  const { library } = useLibrary();
  const {
    filters,
    selectedCollection,
    collections,
    updateFilters,
    updateSortBy,
    resetFilters,
    selectCollection,
  } = useLibraryCollections();

  // Performance tracking state
  const [isFilteringInProgress, setIsFilteringInProgress] = useState(false);
  const [lastFilterTime, setLastFilterTime] = useState(0);
  const [lastSortTime, setLastSortTime] = useState(0);
  
  // Refs for tracking operations
  const filterTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isInitialRender = useRef(true);

  // Debounced operations for better performance
  const debouncedUpdateFilters = useMemo(
    () => createDebouncedFunction(
      (newFilters: Partial<LibraryFilters>) => {
        updateFilters(newFilters);
        setIsFilteringInProgress(false);
      },
      LIBRARY_PERFORMANCE_CONFIG.DEBOUNCE_FILTER_DELAY
    ),
    [updateFilters]
  );

  const debouncedSelectCollection = useMemo(
    () => createDebouncedFunction(
      (collectionId: string | null, source?: string) => {
        selectCollection(collectionId, source as any);
        setIsFilteringInProgress(false);
      },
      LIBRARY_PERFORMANCE_CONFIG.DEBOUNCE_COLLECTION_SWITCH
    ),
    [selectCollection]
  );

  // Optimized filter and sort computation with performance monitoring
  const filteredAndSortedGames = useMemo(() => {
    libraryPerformanceMonitor.startTimer('filterAndSort');
    
    const result = optimizedFilterAndSort(
      library,
      filters,
      filters.sortBy,
      selectedCollection,
      collections
    );

    const duration = libraryPerformanceMonitor.endTimer('filterAndSort');
    setLastFilterTime(duration);

    // Skip initial render for performance tracking
    if (!isInitialRender.current) {
      setIsFilteringInProgress(false);
    } else {
      isInitialRender.current = false;
    }

    return result;
  }, [library, filters, selectedCollection, collections]);

  // Optimized search handler
  const handleSearchChange = useCallback((query: string) => {
    setIsFilteringInProgress(true);
    
    // Clear previous timeout
    if (filterTimeoutRef.current) {
      clearTimeout(filterTimeoutRef.current);
    }

    // Set a timeout to show loading state for longer operations
    filterTimeoutRef.current = setTimeout(() => {
      setIsFilteringInProgress(false);
    }, LIBRARY_PERFORMANCE_CONFIG.DEBOUNCE_FILTER_DELAY + 100);

    debouncedUpdateFilters({ searchQuery: query });
  }, [debouncedUpdateFilters]);

  // Optimized filter change handler
  const handleFilterChange = useCallback((newFilters: Partial<LibraryFilters>) => {
    setIsFilteringInProgress(true);
    
    if (filterTimeoutRef.current) {
      clearTimeout(filterTimeoutRef.current);
    }

    filterTimeoutRef.current = setTimeout(() => {
      setIsFilteringInProgress(false);
    }, LIBRARY_PERFORMANCE_CONFIG.DEBOUNCE_FILTER_DELAY + 100);

    debouncedUpdateFilters(newFilters);
  }, [debouncedUpdateFilters]);

  // Optimized sort change handler
  const handleSortChange = useCallback((sortBy: LibrarySortBy) => {
    libraryPerformanceMonitor.startTimer('sort');
    updateSortBy(sortBy);
    const duration = libraryPerformanceMonitor.endTimer('sort');
    setLastSortTime(duration);
  }, [updateSortBy]);

  // Optimized collection change handler
  const handleCollectionChange = useCallback((collectionId: string | null, source?: string) => {
    setIsFilteringInProgress(true);
    
    if (filterTimeoutRef.current) {
      clearTimeout(filterTimeoutRef.current);
    }

    filterTimeoutRef.current = setTimeout(() => {
      setIsFilteringInProgress(false);
    }, LIBRARY_PERFORMANCE_CONFIG.DEBOUNCE_COLLECTION_SWITCH + 100);

    debouncedSelectCollection(collectionId, source);
  }, [debouncedSelectCollection]);

  // Optimized clear filters handler
  const handleClearFilters = useCallback(() => {
    resetFilters();
    selectCollection(null);
    clearLibraryCache();
    setIsFilteringInProgress(false);
    
    if (filterTimeoutRef.current) {
      clearTimeout(filterTimeoutRef.current);
    }
  }, [resetFilters, selectCollection]);

  // Performance utilities
  const clearCache = useCallback(() => {
    clearLibraryCache();
  }, []);

  const getPerformanceMetrics = useCallback(() => {
    return {
      ...libraryPerformanceMonitor.getMetrics(),
      lastFilterTime,
      lastSortTime,
      totalGames: library.length,
      filteredGames: filteredAndSortedGames.length,
      cacheStats: {
        // Add cache statistics if needed
      }
    };
  }, [lastFilterTime, lastSortTime, library.length, filteredAndSortedGames.length]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearLibraryCache();
      debouncedUpdateFilters.cancel();
      debouncedSelectCollection.cancel();
      
      if (filterTimeoutRef.current) {
        clearTimeout(filterTimeoutRef.current);
      }
    };
  }, [debouncedUpdateFilters, debouncedSelectCollection]);

  // Performance monitoring effect
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      const metrics = getPerformanceMetrics();
      if (metrics.lastFilterTime > LIBRARY_PERFORMANCE_CONFIG.MAX_FILTER_TIME) {
        console.warn(`Library filtering is slow: ${metrics.lastFilterTime}ms`);
      }
    }
  }, [getPerformanceMetrics]);

  return {
    // Core data
    library,
    filteredAndSortedGames,
    
    // Loading states
    isLoading: false, // Can be connected to actual loading state if needed
    isFilteringInProgress,
    
    // Performance metrics
    lastFilterTime,
    lastSortTime,
    
    // Optimized handlers
    handleSearchChange,
    handleFilterChange,
    handleSortChange,
    handleCollectionChange,
    handleClearFilters,
    
    // Performance utilities
    clearCache,
    getPerformanceMetrics,
  };
}

/**
 * Hook for monitoring library performance in development
 */
export function useLibraryPerformanceMonitor() {
  const [metrics, setMetrics] = useState<Record<string, any>>({});

  useEffect(() => {
    if (process.env.NODE_ENV !== 'development') return;

    const interval = setInterval(() => {
      setMetrics(libraryPerformanceMonitor.getMetrics());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  return metrics;
}

/**
 * Hook for detecting performance issues
 */
export function useLibraryPerformanceWarnings() {
  const [warnings, setWarnings] = useState<string[]>([]);

  useEffect(() => {
    if (process.env.NODE_ENV !== 'development') return;

    const checkPerformance = () => {
      const metrics = libraryPerformanceMonitor.getMetrics();
      const newWarnings: string[] = [];

      Object.entries(metrics).forEach(([operation, data]) => {
        if (data.average > LIBRARY_PERFORMANCE_CONFIG.MAX_FILTER_TIME) {
          newWarnings.push(`${operation} is averaging ${data.average.toFixed(2)}ms`);
        }
      });

      setWarnings(newWarnings);
    };

    const interval = setInterval(checkPerformance, 5000);
    return () => clearInterval(interval);
  }, []);

  return warnings;
}
