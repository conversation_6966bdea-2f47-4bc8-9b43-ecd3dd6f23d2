/**
 * Optimized Game Image Styles
 * 
 * Styles for high-performance image loading component with loading states,
 * error handling, and smooth transitions.
 */

.optimized-game-image {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: var(--color-background-secondary);
  border-radius: 4px;
  
  // Performance optimizations
  contain: layout style paint;
  transform: translateZ(0);
}

.optimized-game-image__img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease, filter 0.3s ease;
  
  // Optimize image rendering
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  
  // Default state
  opacity: 1;
  filter: none;
  
  // Loading state
  &--loading {
    opacity: 0.7;
    filter: blur(1px);
  }
  
  // Error state
  &--error {
    opacity: 0.5;
    filter: grayscale(100%);
  }
  
  // Placeholder state
  &--placeholder {
    opacity: 0.8;
    filter: grayscale(50%);
  }
  
  // Smooth fade-in when loaded
  &:not(.optimized-game-image__img--loading):not(.optimized-game-image__img--error) {
    animation: fadeIn 0.3s ease;
  }
}

.optimized-game-image__loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(2px);
  z-index: 2;
}

.optimized-game-image__spinner {
  width: 24px;
  height: 24px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.optimized-game-image__error {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-background-tertiary);
  color: var(--color-text-secondary);
  font-size: 0.75rem;
  text-align: center;
  z-index: 2;
  
  span {
    padding: 8px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 4px;
  }
}

// Animations
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Shimmer effect for loading placeholder
.optimized-game-image__img--loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent 25%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  z-index: 1;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

// Hover effects for interactive images
.optimized-game-image {
  &:hover {
    .optimized-game-image__img:not(.optimized-game-image__img--loading):not(.optimized-game-image__img--error) {
      transform: scale(1.02);
      transition: transform 0.3s ease;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .optimized-game-image__spinner {
    width: 20px;
    height: 20px;
  }
  
  .optimized-game-image__error {
    font-size: 0.625rem;
    
    span {
      padding: 6px;
    }
  }
}

// High DPI display optimizations
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .optimized-game-image__img {
    image-rendering: -webkit-optimize-contrast;
  }
}

// Reduced motion preferences
@media (prefers-reduced-motion: reduce) {
  .optimized-game-image__img {
    transition: none;
    animation: none;
    
    &:hover {
      transform: none;
    }
  }
  
  .optimized-game-image__spinner {
    animation: none;
  }
  
  @keyframes shimmer {
    0%, 100% {
      background-position: 0 0;
    }
  }
  
  @keyframes fadeIn {
    from, to {
      opacity: 1;
    }
  }
}

// Dark mode adjustments
@media (prefers-color-scheme: dark) {
  .optimized-game-image {
    background: var(--color-background-tertiary);
  }
  
  .optimized-game-image__loading {
    background: rgba(0, 0, 0, 0.5);
  }
  
  .optimized-game-image__error {
    background: var(--color-background-quaternary);
    
    span {
      background: rgba(0, 0, 0, 0.7);
    }
  }
}

// Print styles
@media print {
  .optimized-game-image__loading,
  .optimized-game-image__spinner {
    display: none;
  }
  
  .optimized-game-image__img {
    opacity: 1 !important;
    filter: none !important;
  }
}

// Accessibility improvements
.optimized-game-image {
  // Focus styles for keyboard navigation
  &:focus-within {
    outline: 2px solid var(--color-accent);
    outline-offset: 2px;
  }
  
  // High contrast mode support
  @media (prefers-contrast: high) {
    border: 1px solid var(--color-border);
    
    .optimized-game-image__error {
      border: 1px solid var(--color-text-primary);
      background: var(--color-background-primary);
      color: var(--color-text-primary);
    }
  }
}

// Performance optimizations for large grids
.virtual-game-grid .optimized-game-image {
  // Use GPU acceleration
  transform: translate3d(0, 0, 0);
  
  // Optimize repaints
  backface-visibility: hidden;
  perspective: 1000px;
  
  // Contain layout changes
  contain: layout style paint size;
}

// Loading skeleton for better perceived performance
.optimized-game-image--skeleton {
  background: linear-gradient(
    90deg,
    var(--color-background-secondary) 25%,
    var(--color-background-tertiary) 50%,
    var(--color-background-secondary) 75%
  );
  background-size: 200% 100%;
  animation: skeletonPulse 1.5s ease-in-out infinite;
}

@keyframes skeletonPulse {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

// Error retry button
.optimized-game-image__retry {
  position: absolute;
  bottom: 8px;
  right: 8px;
  padding: 4px 8px;
  background: var(--color-background-button);
  border: 1px solid var(--color-border);
  border-radius: 4px;
  color: var(--color-text-secondary);
  font-size: 0.625rem;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 3;
  
  &:hover {
    background: var(--color-background-button-hover);
    color: var(--color-text-primary);
  }
  
  &:active {
    transform: scale(0.95);
  }
}
