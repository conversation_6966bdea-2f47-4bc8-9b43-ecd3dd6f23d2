/**
 * Optimized Game Image Component
 * 
 * High-performance image loading component with lazy loading, caching,
 * progressive enhancement, and error handling for game cover images.
 */

import React, { memo, useCallback, useEffect, useRef, useState } from "react";
import { LIBRARY_PERFORMANCE_CONFIG } from "@renderer/config/library-performance";

interface OptimizedGameImageProps {
  src?: string | null;
  alt: string;
  className?: string;
  width?: number;
  height?: number;
  placeholder?: string;
  onLoad?: () => void;
  onError?: () => void;
  priority?: boolean; // For above-the-fold images
}

interface ImageCache {
  [key: string]: {
    blob: Blob;
    timestamp: number;
    url: string;
  };
}

// Global image cache
const imageCache: ImageCache = {};
const loadingImages = new Set<string>();

/**
 * Cache management utilities
 */
class ImageCacheManager {
  private static instance: ImageCacheManager;
  private cache: ImageCache = imageCache;
  private maxCacheSize = LIBRARY_PERFORMANCE_CONFIG.IMAGE_CACHE_SIZE;

  static getInstance(): ImageCacheManager {
    if (!ImageCacheManager.instance) {
      ImageCacheManager.instance = new ImageCacheManager();
    }
    return ImageCacheManager.instance;
  }

  get(src: string): string | null {
    const cached = this.cache[src];
    if (!cached) return null;

    // Check if cache entry is still valid
    const age = Date.now() - cached.timestamp;
    const maxAge = 30 * 60 * 1000; // 30 minutes

    if (age > maxAge) {
      this.remove(src);
      return null;
    }

    return cached.url;
  }

  set(src: string, blob: Blob): string {
    // Clean up old entries if cache is full
    if (Object.keys(this.cache).length >= this.maxCacheSize) {
      this.cleanup();
    }

    const url = URL.createObjectURL(blob);
    this.cache[src] = {
      blob,
      timestamp: Date.now(),
      url,
    };

    return url;
  }

  remove(src: string): void {
    const cached = this.cache[src];
    if (cached) {
      URL.revokeObjectURL(cached.url);
      delete this.cache[src];
    }
  }

  private cleanup(): void {
    const entries = Object.entries(this.cache);
    const now = Date.now();

    // Sort by timestamp (oldest first)
    entries.sort((a, b) => a[1].timestamp - b[1].timestamp);

    // Remove oldest 25% of entries
    const toRemove = Math.floor(entries.length * 0.25);
    for (let i = 0; i < toRemove; i++) {
      const [src] = entries[i];
      this.remove(src);
    }
  }

  clear(): void {
    Object.keys(this.cache).forEach(src => this.remove(src));
  }
}

const cacheManager = ImageCacheManager.getInstance();

/**
 * Custom hook for optimized image loading
 */
function useOptimizedImage(src?: string | null, priority = false) {
  const [imageState, setImageState] = useState<{
    src: string | null;
    isLoading: boolean;
    hasError: boolean;
    isIntersecting: boolean;
  }>({
    src: null,
    isLoading: false,
    hasError: false,
    isIntersecting: false,
  });

  const imgRef = useRef<HTMLImageElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const retryCountRef = useRef(0);

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (!src || priority) {
      setImageState(prev => ({ ...prev, isIntersecting: true }));
      return;
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setImageState(prev => ({ ...prev, isIntersecting: true }));
          observer.disconnect();
        }
      },
      {
        threshold: LIBRARY_PERFORMANCE_CONFIG.IMAGE_LAZY_LOADING_THRESHOLD,
        rootMargin: '50px',
      }
    );

    observerRef.current = observer;

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, [src, priority]);

  // Load image when intersecting
  useEffect(() => {
    if (!src || !imageState.isIntersecting || imageState.src) return;

    const loadImage = async () => {
      if (loadingImages.has(src)) return;

      setImageState(prev => ({ ...prev, isLoading: true, hasError: false }));

      try {
        // Check cache first
        const cachedUrl = cacheManager.get(src);
        if (cachedUrl) {
          setImageState(prev => ({
            ...prev,
            src: cachedUrl,
            isLoading: false,
          }));
          return;
        }

        loadingImages.add(src);

        // Load image with timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => {
          controller.abort();
        }, LIBRARY_PERFORMANCE_CONFIG.IMAGE_LOADING_TIMEOUT);

        const response = await fetch(src, {
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`);
        }

        const blob = await response.blob();
        const objectUrl = cacheManager.set(src, blob);

        setImageState(prev => ({
          ...prev,
          src: objectUrl,
          isLoading: false,
        }));

      } catch (error) {
        console.warn(`Failed to load image: ${src}`, error);
        
        // Retry logic
        if (retryCountRef.current < LIBRARY_PERFORMANCE_CONFIG.IMAGE_RETRY_ATTEMPTS) {
          retryCountRef.current++;
          setTimeout(() => {
            setImageState(prev => ({ ...prev, isLoading: false }));
            // Trigger reload by clearing src
            setImageState(prev => ({ ...prev, src: null }));
          }, LIBRARY_PERFORMANCE_CONFIG.IMAGE_RETRY_DELAY);
        } else {
          setImageState(prev => ({
            ...prev,
            isLoading: false,
            hasError: true,
          }));
        }
      } finally {
        loadingImages.delete(src);
      }
    };

    loadImage();
  }, [src, imageState.isIntersecting, imageState.src]);

  return {
    ...imageState,
    imgRef,
  };
}

/**
 * Optimized Game Image Component
 */
export const OptimizedGameImage = memo<OptimizedGameImageProps>(({
  src,
  alt,
  className = "",
  width,
  height,
  placeholder = "/placeholder-game.jpg",
  onLoad,
  onError,
  priority = false,
}) => {
  const { src: imageSrc, isLoading, hasError, imgRef } = useOptimizedImage(src, priority);

  const handleLoad = useCallback(() => {
    onLoad?.();
  }, [onLoad]);

  const handleError = useCallback(() => {
    onError?.();
  }, [onError]);

  // Show placeholder while loading or on error
  const shouldShowPlaceholder = !imageSrc || hasError;
  const displaySrc = shouldShowPlaceholder ? placeholder : imageSrc;

  return (
    <div className={`optimized-game-image ${className}`}>
      <img
        ref={imgRef}
        src={displaySrc}
        alt={alt}
        width={width}
        height={height}
        onLoad={handleLoad}
        onError={handleError}
        className={`
          optimized-game-image__img
          ${isLoading ? 'optimized-game-image__img--loading' : ''}
          ${hasError ? 'optimized-game-image__img--error' : ''}
          ${shouldShowPlaceholder ? 'optimized-game-image__img--placeholder' : ''}
        `.trim()}
        loading={priority ? "eager" : "lazy"}
        decoding="async"
      />
      
      {isLoading && (
        <div className="optimized-game-image__loading">
          <div className="optimized-game-image__spinner" />
        </div>
      )}
      
      {hasError && !placeholder && (
        <div className="optimized-game-image__error">
          <span>Failed to load image</span>
        </div>
      )}
    </div>
  );
});

OptimizedGameImage.displayName = "OptimizedGameImage";

/**
 * Hook for preloading images
 */
export function useImagePreloader(urls: string[], enabled = true) {
  const [preloadedCount, setPreloadedCount] = useState(0);

  useEffect(() => {
    if (!enabled || urls.length === 0) return;

    let cancelled = false;
    let loadedCount = 0;

    const preloadImage = async (url: string) => {
      try {
        // Check if already cached
        if (cacheManager.get(url)) {
          loadedCount++;
          if (!cancelled) {
            setPreloadedCount(loadedCount);
          }
          return;
        }

        const response = await fetch(url);
        if (!response.ok) throw new Error(`HTTP ${response.status}`);
        
        const blob = await response.blob();
        cacheManager.set(url, blob);
        
        loadedCount++;
        if (!cancelled) {
          setPreloadedCount(loadedCount);
        }
      } catch (error) {
        console.warn(`Failed to preload image: ${url}`, error);
      }
    };

    // Preload images with limited concurrency
    const preloadBatch = async () => {
      const batchSize = 3; // Limit concurrent requests
      for (let i = 0; i < urls.length; i += batchSize) {
        if (cancelled) break;
        
        const batch = urls.slice(i, i + batchSize);
        await Promise.allSettled(batch.map(preloadImage));
      }
    };

    preloadBatch();

    return () => {
      cancelled = true;
    };
  }, [urls, enabled]);

  return { preloadedCount, total: urls.length };
}

/**
 * Utility function to clear image cache
 */
export function clearImageCache(): void {
  cacheManager.clear();
}

/**
 * Hook for monitoring image cache performance
 */
export function useImageCacheStats() {
  const [stats, setStats] = useState({
    cacheSize: 0,
    hitRate: 0,
    memoryUsage: 0,
  });

  useEffect(() => {
    const updateStats = () => {
      const cacheSize = Object.keys(imageCache).length;
      // Calculate approximate memory usage
      const memoryUsage = Object.values(imageCache).reduce(
        (total, entry) => total + entry.blob.size,
        0
      );

      setStats({
        cacheSize,
        hitRate: 0, // Would need to track hits/misses
        memoryUsage,
      });
    };

    updateStats();
    const interval = setInterval(updateStats, 5000);

    return () => clearInterval(interval);
  }, []);

  return stats;
}
