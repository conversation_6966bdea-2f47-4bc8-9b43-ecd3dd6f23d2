/**
 * Optimized Library Utilities
 * 
 * High-performance filtering, sorting, and processing utilities for the library page.
 * These utilities are designed to handle large game collections efficiently.
 */

import type { LibraryGame, LibrarySortBy, LibraryFilters } from "@types";
import { libraryPerformanceMonitor, LIBRARY_PERFORMANCE_CONFIG } from "@config/library-performance";
import { wasPlayedRecently } from "./date-utils";
import { filterGamesByGenres } from "./genre-utils";

/**
 * Cache for expensive operations
 */
class LibraryCache {
  private cache = new Map<string, any>();
  private timestamps = new Map<string, number>();
  private readonly TTL = 5 * 60 * 1000; // 5 minutes

  set(key: string, value: any): void {
    this.cache.set(key, value);
    this.timestamps.set(key, Date.now());
    this.cleanup();
  }

  get(key: string): any | null {
    const timestamp = this.timestamps.get(key);
    if (!timestamp || Date.now() - timestamp > this.TTL) {
      this.cache.delete(key);
      this.timestamps.delete(key);
      return null;
    }
    return this.cache.get(key) || null;
  }

  private cleanup(): void {
    const now = Date.now();
    for (const [key, timestamp] of this.timestamps.entries()) {
      if (now - timestamp > this.TTL) {
        this.cache.delete(key);
        this.timestamps.delete(key);
      }
    }
  }

  clear(): void {
    this.cache.clear();
    this.timestamps.clear();
  }
}

const libraryCache = new LibraryCache();

/**
 * Optimized game filtering with caching and chunked processing
 */
export function optimizedFilterGames(
  games: LibraryGame[],
  filters: LibraryFilters,
  selectedCollection?: string | null,
  collections?: any[]
): LibraryGame[] {
  libraryPerformanceMonitor.startTimer('filter');

  // Create cache key
  const cacheKey = JSON.stringify({
    gameCount: games.length,
    filters,
    selectedCollection,
    collectionsHash: collections?.length || 0
  });

  // Check cache first
  const cached = libraryCache.get(cacheKey);
  if (cached) {
    libraryPerformanceMonitor.endTimer('filter');
    return cached;
  }

  let filteredGames = games;

  // Apply collection filter first (most selective)
  if (selectedCollection) {
    filteredGames = filterByCollection(filteredGames, selectedCollection, collections);
  }

  // Apply other filters in order of selectivity
  if (filters.showInstalledOnly) {
    filteredGames = filteredGames.filter(game => Boolean(game.executablePath));
  } else if (filters.showNotInstalledOnly) {
    filteredGames = filteredGames.filter(game => !Boolean(game.executablePath));
  }

  // Genre filtering (potentially expensive)
  if (filters.genres.length > 0) {
    filteredGames = filterGamesByGenres(filteredGames, filters.genres);
  }

  // Search filtering (most expensive, do last)
  if (filters.searchQuery && typeof filters.searchQuery === 'string' && filters.searchQuery.length >= 2) {
    filteredGames = filterBySearch(filteredGames, filters.searchQuery);
  }

  // Cache the result
  libraryCache.set(cacheKey, filteredGames);
  
  const duration = libraryPerformanceMonitor.endTimer('filter');
  
  // Warn if filtering is too slow
  if (duration > LIBRARY_PERFORMANCE_CONFIG.MAX_FILTER_TIME) {
    console.warn(`Filtering took ${duration}ms for ${games.length} games`);
  }

  return filteredGames;
}

/**
 * Optimized collection filtering
 */
function filterByCollection(
  games: LibraryGame[],
  selectedCollection: string,
  collections?: any[]
): LibraryGame[] {
  // Smart collections
  switch (selectedCollection) {
    case "recently-played":
      return games.filter(game => wasPlayedRecently(game.lastTimePlayed));
    case "favorites":
      return games.filter(game => game.favorite);
    case "installed":
      return games.filter(game => Boolean(game.executablePath));
    case "not-played":
      return games.filter(game => !game.playTimeInMilliseconds || game.playTimeInMilliseconds === 0);
    default:
      // Regular collection
      const collection = collections?.find(c => c.id === selectedCollection);
      if (collection) {
        // Use Set for O(1) lookup instead of Array.includes O(n)
        const gameIdSet = new Set(collection.gameIds);
        return games.filter(game => gameIdSet.has(game.id));
      }
      return games;
  }
}

/**
 * Optimized search filtering with chunked processing
 */
function filterBySearch(games: LibraryGame[], searchQuery: string): LibraryGame[] {
  const query = searchQuery.toLowerCase();
  const chunkSize = LIBRARY_PERFORMANCE_CONFIG.CHUNK_SIZE_FOR_PROCESSING;
  
  if (games.length <= chunkSize) {
    return games.filter(game => game.title.toLowerCase().includes(query));
  }

  // Process in chunks for large datasets
  const results: LibraryGame[] = [];
  for (let i = 0; i < games.length; i += chunkSize) {
    const chunk = games.slice(i, i + chunkSize);
    const filteredChunk = chunk.filter(game => game.title.toLowerCase().includes(query));
    results.push(...filteredChunk);
  }

  return results;
}

/**
 * Optimized game sorting with performance monitoring
 */
export function optimizedSortGames(games: LibraryGame[], sortBy: LibrarySortBy): LibraryGame[] {
  libraryPerformanceMonitor.startTimer('sort');

  // Don't sort if not necessary
  if (games.length <= 1) {
    libraryPerformanceMonitor.endTimer('sort');
    return games;
  }

  // Create cache key
  const cacheKey = `sort_${games.length}_${sortBy}_${games[0]?.id || ''}_${games[games.length - 1]?.id || ''}`;
  const cached = libraryCache.get(cacheKey);
  if (cached) {
    libraryPerformanceMonitor.endTimer('sort');
    return cached;
  }

  // Create a copy to avoid mutating original array
  const sortedGames = [...games];

  switch (sortBy) {
    case "name-asc":
      sortedGames.sort((a, b) => a.title.localeCompare(b.title));
      break;
    case "name-desc":
      sortedGames.sort((a, b) => b.title.localeCompare(a.title));
      break;
    case "last-played-asc":
      sortedGames.sort((a, b) => {
        const aTime = getTimestamp(a.lastTimePlayed) || 0;
        const bTime = getTimestamp(b.lastTimePlayed) || 0;
        return aTime - bTime;
      });
      break;
    case "last-played-desc":
      sortedGames.sort((a, b) => {
        const aTime = getTimestamp(a.lastTimePlayed) || 0;
        const bTime = getTimestamp(b.lastTimePlayed) || 0;
        return bTime - aTime;
      });
      break;
    case "playtime-asc":
      sortedGames.sort((a, b) => (a.playTimeInMilliseconds || 0) - (b.playTimeInMilliseconds || 0));
      break;
    case "playtime-desc":
      sortedGames.sort((a, b) => (b.playTimeInMilliseconds || 0) - (a.playTimeInMilliseconds || 0));
      break;
    case "status-asc":
      sortedGames.sort((a, b) => Number(Boolean(a.executablePath)) - Number(Boolean(b.executablePath)));
      break;
    case "status-desc":
      sortedGames.sort((a, b) => Number(Boolean(b.executablePath)) - Number(Boolean(a.executablePath)));
      break;
    default:
      // No sorting needed
      break;
  }

  // Cache the result
  libraryCache.set(cacheKey, sortedGames);

  const duration = libraryPerformanceMonitor.endTimer('sort');
  
  // Warn if sorting is too slow
  if (duration > LIBRARY_PERFORMANCE_CONFIG.MAX_SORT_TIME) {
    console.warn(`Sorting took ${duration}ms for ${games.length} games`);
  }

  return sortedGames;
}

/**
 * Helper function to get timestamp from various date formats
 */
function getTimestamp(date: Date | string | null | undefined): number | null {
  if (!date) return null;
  
  if (typeof date === 'string') {
    const parsed = new Date(date);
    return isNaN(parsed.getTime()) ? null : parsed.getTime();
  }
  
  if (date instanceof Date) {
    return isNaN(date.getTime()) ? null : date.getTime();
  }
  
  return null;
}

/**
 * Combined filter and sort operation for maximum efficiency
 */
export function optimizedFilterAndSort(
  games: LibraryGame[],
  filters: LibraryFilters,
  sortBy: LibrarySortBy,
  selectedCollection?: string | null,
  collections?: any[]
): LibraryGame[] {
  libraryPerformanceMonitor.startTimer('filterAndSort');

  // Filter first
  const filteredGames = optimizedFilterGames(games, filters, selectedCollection, collections);
  
  // Then sort
  const sortedGames = optimizedSortGames(filteredGames, sortBy);

  libraryPerformanceMonitor.endTimer('filterAndSort');
  
  return sortedGames;
}

/**
 * Debounced function factory for search and filter operations
 */
export function createDebouncedFunction<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): T & { cancel: () => void } {
  let timeoutId: NodeJS.Timeout | null = null;

  const debouncedFunction = ((...args: Parameters<T>) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    timeoutId = setTimeout(() => {
      func(...args);
      timeoutId = null;
    }, delay);
  }) as T & { cancel: () => void };

  debouncedFunction.cancel = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }
  };

  return debouncedFunction;
}

/**
 * Clear all caches (useful for memory management)
 */
export function clearLibraryCache(): void {
  libraryCache.clear();
}

/**
 * Get cache statistics for debugging
 */
export function getLibraryCacheStats(): { size: number; keys: string[] } {
  return {
    size: (libraryCache as any).cache.size,
    keys: Array.from((libraryCache as any).cache.keys())
  };
}
