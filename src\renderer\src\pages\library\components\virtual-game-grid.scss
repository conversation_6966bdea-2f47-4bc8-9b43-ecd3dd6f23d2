/**
 * Virtual Game Grid Styles
 * 
 * Optimized styles for virtual scrolling game grid with performance considerations.
 */

.virtual-game-grid {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;

  // Ensure smooth scrolling
  scroll-behavior: smooth;
  
  // Hardware acceleration for better performance
  transform: translateZ(0);
  will-change: scroll-position;

  // Custom scrollbar styling
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--color-background-secondary);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--color-border);
    border-radius: 4px;
    
    &:hover {
      background: var(--color-border-hover);
    }
  }
}

.virtual-grid-item {
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  // Optimize rendering performance
  contain: layout style paint;
  transform: translateZ(0);
}

.library-game-grid {
  width: 100%;
  min-height: 100%;
  
  // Fallback grid for non-virtual mode
  &:not(.virtual-game-grid) {
    display: grid;
    gap: 16px;
    padding: 16px;
    
    // Responsive grid columns
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    
    @media (min-width: 768px) {
      grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    }
    
    @media (min-width: 1200px) {
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    }
  }
}

.library-game-card {
  position: relative;
  background: var(--color-background-card);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s ease;
  cursor: pointer;
  
  // Performance optimizations
  contain: layout style paint;
  transform: translateZ(0);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-color: var(--color-border-hover);
  }
  
  &:active {
    transform: translateY(0);
  }

  // Card size variants
  &--compact {
    width: 200px;
    height: 240px;
    
    .library-game-card__image {
      height: 160px;
    }
    
    .library-game-card__title {
      font-size: 0.875rem;
      line-height: 1.2;
    }
  }

  &--normal {
    width: 240px;
    height: 280px;
    
    .library-game-card__image {
      height: 200px;
    }
    
    .library-game-card__title {
      font-size: 1rem;
      line-height: 1.3;
    }
  }

  &--large {
    width: 280px;
    height: 320px;
    
    .library-game-card__image {
      height: 240px;
    }
    
    .library-game-card__title {
      font-size: 1.125rem;
      line-height: 1.4;
    }
  }
}

.library-game-card__image {
  width: 100%;
  position: relative;
  overflow: hidden;
  background: var(--color-background-secondary);
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
    
    // Optimize image rendering
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    
    // Lazy loading optimization
    &[loading="lazy"] {
      opacity: 0;
      transition: opacity 0.3s ease;
      
      &.loaded {
        opacity: 1;
      }
    }
  }
  
  // Hover effect for images
  .library-game-card:hover & img {
    transform: scale(1.05);
  }
  
  // Loading placeholder
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      90deg,
      var(--color-background-secondary) 25%,
      var(--color-background-tertiary) 50%,
      var(--color-background-secondary) 75%
    );
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    z-index: 1;
  }
  
  img + &::before {
    display: none;
  }
}

.library-game-card__content {
  padding: 12px;
  height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.library-game-card__title {
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0 0 8px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: 1.3;
}

.library-game-card__actions {
  display: flex;
  gap: 8px;
  margin-top: auto;
  
  button {
    flex: 1;
    padding: 6px 12px;
    border: 1px solid var(--color-border);
    border-radius: 4px;
    background: var(--color-background-button);
    color: var(--color-text-secondary);
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover {
      background: var(--color-background-button-hover);
      border-color: var(--color-border-hover);
      color: var(--color-text-primary);
    }
    
    &:active {
      transform: scale(0.98);
    }
  }
}

// Debug information for development
.virtual-grid-debug {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 0.75rem;
  pointer-events: none;
  z-index: 1000;
  
  // Hide in production
  .production & {
    display: none;
  }
}

// Animations
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

// Performance optimizations for large lists
.virtual-game-grid {
  // Use GPU acceleration
  transform: translate3d(0, 0, 0);
  
  // Optimize repaints
  .virtual-grid-item {
    backface-visibility: hidden;
    perspective: 1000px;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .library-game-card {
    &--compact,
    &--normal,
    &--large {
      width: 100%;
      max-width: 200px;
      height: 240px;
      
      .library-game-card__image {
        height: 160px;
      }
    }
  }
  
  .library-game-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 12px;
    padding: 12px;
  }
}

// High DPI display optimizations
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .library-game-card__image img {
    image-rendering: -webkit-optimize-contrast;
  }
}

// Reduced motion preferences
@media (prefers-reduced-motion: reduce) {
  .library-game-card {
    transition: none;
    
    &:hover {
      transform: none;
    }
    
    img {
      transition: none;
    }
  }
  
  .virtual-game-grid {
    scroll-behavior: auto;
  }
  
  @keyframes shimmer {
    0%, 100% {
      background-position: 0 0;
    }
  }
}
