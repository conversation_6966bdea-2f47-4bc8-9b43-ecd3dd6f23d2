/**
 * Library Performance Configuration
 * 
 * Centralized performance settings and optimization parameters for the library page.
 * These settings are designed to provide the best user experience across different
 * library sizes and device capabilities.
 */

/**
 * Core performance thresholds and limits
 */
export const LIBRARY_PERFORMANCE_CONFIG = {
  // Filtering and sorting performance
  DEBOUNCE_SEARCH_DELAY: 300, // 300ms debounce for search input
  DEBOUNCE_FILTER_DELAY: 150, // 150ms debounce for filter changes
  DEBOUNCE_COLLECTION_SWITCH: 100, // 100ms debounce for collection switching
  MAX_GAMES_BEFORE_VIRTUALIZATION: 100, // Enable virtual scrolling after 100 games
  CHUNK_SIZE_FOR_PROCESSING: 50, // Process games in chunks of 50
  
  // Image loading and caching
  IMAGE_LAZY_LOADING_THRESHOLD: 0.1, // 10% visibility to start loading
  IMAGE_PRELOAD_COUNT: 10, // Preload next 10 images in viewport
  IMAGE_CACHE_SIZE: 200, // Cache up to 200 images in memory
  IMAGE_LOADING_TIMEOUT: 5000, // 5 seconds timeout for image loading
  IMAGE_RETRY_ATTEMPTS: 2, // Retry failed images 2 times
  IMAGE_RETRY_DELAY: 1000, // 1 second delay between retries
  
  // Virtual scrolling configuration
  VIRTUAL_ITEM_HEIGHT_GRID: 280, // Height of each game card in grid mode
  VIRTUAL_ITEM_HEIGHT_LIST: 80, // Height of each game card in list mode
  VIRTUAL_OVERSCAN: 5, // Render 5 extra items outside viewport
  VIRTUAL_BUFFER_SIZE: 20, // Keep 20 items in buffer for smooth scrolling
  
  // UI responsiveness
  SKELETON_DISPLAY_MIN_TIME: 200, // Show skeleton for at least 200ms
  TRANSITION_DURATION: 200, // Standard transition duration
  LOADING_SPINNER_DELAY: 300, // Show loading spinner after 300ms
  
  // Memory management
  MAX_CACHED_COLLECTIONS: 10, // Cache up to 10 collection filter results
  MAX_CACHED_SEARCHES: 20, // Cache up to 20 search results
  CLEANUP_INTERVAL: 5 * 60 * 1000, // Cleanup cache every 5 minutes
  MEMORY_PRESSURE_THRESHOLD: 0.8, // Start cleanup at 80% memory usage
  
  // Performance budgets (in milliseconds)
  MAX_FILTER_TIME: 100, // Maximum time for filtering operation
  MAX_SORT_TIME: 50, // Maximum time for sorting operation
  MAX_RENDER_TIME: 16, // Maximum time for render cycle (60fps)
  MAX_COLLECTION_SWITCH_TIME: 200, // Maximum time for collection switching
  
  // Batch processing
  BATCH_UPDATE_SIZE: 25, // Update UI in batches of 25 items
  BATCH_UPDATE_DELAY: 10, // 10ms delay between batches
  
  // Error handling
  MAX_RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // 1 second
  ERROR_DISPLAY_DURATION: 5000, // 5 seconds
} as const;

/**
 * Performance monitoring configuration
 */
export const LIBRARY_PERFORMANCE_MONITORING = {
  ENABLE_LOGGING: process.env.NODE_ENV === 'development',
  LOG_FILTER_TIMES: true,
  LOG_SORT_TIMES: true,
  LOG_RENDER_TIMES: true,
  LOG_IMAGE_LOADING: true,
  LOG_MEMORY_USAGE: true,
  WARN_SLOW_OPERATIONS: true,
  SLOW_OPERATION_THRESHOLD: 100, // 100ms
  MEMORY_WARNING_THRESHOLD: 100 * 1024 * 1024, // 100MB
} as const;

/**
 * Feature flags for gradual rollout and A/B testing
 */
export const LIBRARY_FEATURE_FLAGS = {
  USE_VIRTUAL_SCROLLING: true,
  USE_OPTIMIZED_FILTERING: true,
  USE_DEBOUNCED_SEARCH: true,
  USE_IMAGE_PRELOADING: true,
  USE_MEMORY_OPTIMIZATION: true,
  USE_BATCH_UPDATES: true,
  USE_INTERSECTION_OBSERVER: true,
  USE_WEB_WORKERS: false, // Disabled for now, can be enabled for heavy computations
  USE_SKELETON_SCREENS: true,
  USE_PROGRESSIVE_LOADING: true,
} as const;

/**
 * Device-specific optimizations
 */
export const DEVICE_OPTIMIZATIONS = {
  // Steam Deck specific optimizations
  STEAM_DECK: {
    VIRTUAL_OVERSCAN: 3, // Reduced overscan for limited memory
    IMAGE_CACHE_SIZE: 100, // Reduced cache size
    CHUNK_SIZE_FOR_PROCESSING: 25, // Smaller chunks
    DEBOUNCE_SEARCH_DELAY: 400, // Longer debounce for touch input
  },
  
  // Low-end device optimizations
  LOW_END: {
    VIRTUAL_OVERSCAN: 2,
    IMAGE_CACHE_SIZE: 50,
    CHUNK_SIZE_FOR_PROCESSING: 20,
    DEBOUNCE_SEARCH_DELAY: 500,
    USE_SKELETON_SCREENS: false, // Disable for performance
  },
  
  // High-end device optimizations
  HIGH_END: {
    VIRTUAL_OVERSCAN: 10,
    IMAGE_CACHE_SIZE: 500,
    CHUNK_SIZE_FOR_PROCESSING: 100,
    DEBOUNCE_SEARCH_DELAY: 200,
    IMAGE_PRELOAD_COUNT: 20,
  },
} as const;

/**
 * Performance metrics tracking
 */
export interface LibraryPerformanceMetrics {
  filterTime: number;
  sortTime: number;
  renderTime: number;
  imageLoadTime: number;
  memoryUsage: number;
  collectionSwitchTime: number;
  searchTime: number;
  totalGames: number;
  visibleGames: number;
}

/**
 * Performance optimization utilities
 */
export class LibraryPerformanceMonitor {
  private metrics = new Map<string, number[]>();
  private startTimes = new Map<string, number>();

  startTimer(operation: string): void {
    this.startTimes.set(operation, performance.now());
  }

  endTimer(operation: string): number {
    const startTime = this.startTimes.get(operation);
    if (!startTime) return 0;

    const duration = performance.now() - startTime;
    this.recordMetric(operation, duration);
    this.startTimes.delete(operation);

    if (LIBRARY_PERFORMANCE_MONITORING.ENABLE_LOGGING) {
      console.log(`⏱️ Library ${operation}: ${duration.toFixed(2)}ms`);
      
      if (LIBRARY_PERFORMANCE_MONITORING.WARN_SLOW_OPERATIONS && 
          duration > LIBRARY_PERFORMANCE_MONITORING.SLOW_OPERATION_THRESHOLD) {
        console.warn(`🐌 Slow library operation: ${operation} took ${duration.toFixed(2)}ms`);
      }
    }

    return duration;
  }

  private recordMetric(operation: string, duration: number): void {
    if (!this.metrics.has(operation)) {
      this.metrics.set(operation, []);
    }
    
    const metrics = this.metrics.get(operation)!;
    metrics.push(duration);
    
    // Keep only last 100 measurements
    if (metrics.length > 100) {
      metrics.shift();
    }
  }

  getAverageTime(operation: string): number {
    const metrics = this.metrics.get(operation);
    if (!metrics || metrics.length === 0) return 0;
    
    return metrics.reduce((sum, time) => sum + time, 0) / metrics.length;
  }

  getMetrics(): Record<string, { average: number; count: number; latest: number }> {
    const result: Record<string, { average: number; count: number; latest: number }> = {};
    
    for (const [operation, times] of this.metrics.entries()) {
      if (times.length > 0) {
        result[operation] = {
          average: this.getAverageTime(operation),
          count: times.length,
          latest: times[times.length - 1],
        };
      }
    }
    
    return result;
  }

  reset(): void {
    this.metrics.clear();
    this.startTimes.clear();
  }
}

// Global performance monitor instance
export const libraryPerformanceMonitor = new LibraryPerformanceMonitor();

/**
 * Utility function to detect device capabilities
 */
export function detectDeviceCapabilities() {
  const memory = (navigator as any).deviceMemory || 4; // Default to 4GB if not available
  const cores = navigator.hardwareConcurrency || 4; // Default to 4 cores
  
  if (memory <= 2 || cores <= 2) {
    return 'LOW_END';
  } else if (memory >= 8 && cores >= 8) {
    return 'HIGH_END';
  } else {
    return 'STANDARD';
  }
}

/**
 * Get optimized configuration based on device capabilities
 */
export function getOptimizedConfig() {
  const deviceType = detectDeviceCapabilities();
  const baseConfig = LIBRARY_PERFORMANCE_CONFIG;
  
  switch (deviceType) {
    case 'LOW_END':
      return { ...baseConfig, ...DEVICE_OPTIMIZATIONS.LOW_END };
    case 'HIGH_END':
      return { ...baseConfig, ...DEVICE_OPTIMIZATIONS.HIGH_END };
    default:
      return baseConfig;
  }
}
