/**
 * Virtual Game Grid Component
 * 
 * High-performance virtual scrolling grid for large game collections.
 * Only renders visible items to maintain smooth performance with 1000+ games.
 */

import React, { memo, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { FixedSizeGrid as Grid } from "react-window";
import { useTranslation } from "react-i18next";
import type { LibraryGame, LibraryCardSize } from "@types";
import { LIBRARY_PERFORMANCE_CONFIG } from "@renderer/config/library-performance";
import { LibraryGameCard } from "./library-game-card";
import "./virtual-game-grid.scss";

interface VirtualGameGridProps {
  games: LibraryGame[];
  cardSize: LibraryCardSize;
  onAddToCollection?: (game: LibraryGame) => void;
  onRemoveFromCollection?: (game: LibraryGame) => void;
  className?: string;
}

interface GridItemProps {
  columnIndex: number;
  rowIndex: number;
  style: React.CSSProperties;
  data: {
    games: LibraryGame[];
    columnsPerRow: number;
    cardSize: LibraryCardSize;
    onAddToCollection?: (game: LibraryGame) => void;
    onRemoveFromCollection?: (game: LibraryGame) => void;
  };
}

/**
 * Individual grid item component (memoized for performance)
 */
const GridItem = memo<GridItemProps>(({ columnIndex, rowIndex, style, data }) => {
  const { games, columnsPerRow, cardSize, onAddToCollection, onRemoveFromCollection } = data;
  const gameIndex = rowIndex * columnsPerRow + columnIndex;
  const game = games[gameIndex];

  if (!game) {
    return <div style={style} />;
  }

  return (
    <div style={style} className="virtual-grid-item">
      <LibraryGameCard
        game={game}
        cardSize={cardSize}
        onAddToCollection={onAddToCollection}
        onRemoveFromCollection={onRemoveFromCollection}
      />
    </div>
  );
});

GridItem.displayName = "GridItem";

/**
 * Calculate optimal grid dimensions based on container size and card size
 */
function useGridDimensions(cardSize: LibraryCardSize, containerWidth: number) {
  return useMemo(() => {
    // Card dimensions based on size
    const cardDimensions = {
      compact: { width: 200, height: 240 },
      normal: { width: 240, height: 280 },
      large: { width: 280, height: 320 },
    };

    const { width: cardWidth, height: cardHeight } = cardDimensions[cardSize];
    const gap = 16; // Gap between cards

    // Calculate columns that fit in container
    const columnsPerRow = Math.max(1, Math.floor((containerWidth + gap) / (cardWidth + gap)));
    
    // Adjust card width to fill available space
    const adjustedCardWidth = (containerWidth - (gap * (columnsPerRow - 1))) / columnsPerRow;

    return {
      columnsPerRow,
      cardWidth: adjustedCardWidth,
      cardHeight,
      gap,
    };
  }, [cardSize, containerWidth]);
}

/**
 * Custom hook for container size detection
 */
function useContainerSize() {
  const containerRef = useRef<HTMLDivElement>(null);
  const [size, setSize] = useState({ width: 1200, height: 600 });

  useEffect(() => {
    const updateSize = () => {
      if (containerRef.current) {
        const { clientWidth, clientHeight } = containerRef.current;
        setSize({ width: clientWidth, height: clientHeight });
      }
    };

    updateSize();

    const resizeObserver = new ResizeObserver(updateSize);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  return { containerRef, ...size };
}

/**
 * Main Virtual Game Grid Component
 */
export const VirtualGameGrid = memo<VirtualGameGridProps>(({
  games,
  cardSize,
  onAddToCollection,
  onRemoveFromCollection,
  className = "",
}) => {
  const { t } = useTranslation("library");
  const { containerRef, width, height } = useContainerSize();
  const { columnsPerRow, cardWidth, cardHeight } = useGridDimensions(cardSize, width);

  // Calculate grid dimensions
  const rowCount = Math.ceil(games.length / columnsPerRow);
  const shouldUseVirtualization = games.length > LIBRARY_PERFORMANCE_CONFIG.MAX_GAMES_BEFORE_VIRTUALIZATION;

  // Grid data for react-window
  const gridData = useMemo(() => ({
    games,
    columnsPerRow,
    cardSize,
    onAddToCollection,
    onRemoveFromCollection,
  }), [games, columnsPerRow, cardSize, onAddToCollection, onRemoveFromCollection]);

  // Fallback to regular grid for small collections
  if (!shouldUseVirtualization) {
    return (
      <div 
        ref={containerRef}
        className={`library-game-grid ${className}`}
        style={{
          display: 'grid',
          gridTemplateColumns: `repeat(${columnsPerRow}, 1fr)`,
          gap: '16px',
          padding: '16px',
        }}
      >
        {games.map((game) => (
          <LibraryGameCard
            key={game.id}
            game={game}
            cardSize={cardSize}
            onAddToCollection={onAddToCollection}
            onRemoveFromCollection={onRemoveFromCollection}
          />
        ))}
      </div>
    );
  }

  // Virtual scrolling for large collections
  return (
    <div ref={containerRef} className={`virtual-game-grid ${className}`}>
      {width > 0 && height > 0 && (
        <Grid
          columnCount={columnsPerRow}
          columnWidth={cardWidth}
          height={height}
          rowCount={rowCount}
          rowHeight={cardHeight + 16} // Add gap
          width={width}
          itemData={gridData}
          overscanRowCount={LIBRARY_PERFORMANCE_CONFIG.VIRTUAL_OVERSCAN}
          overscanColumnCount={1}
        >
          {GridItem}
        </Grid>
      )}
      
      {/* Performance info for development */}
      {process.env.NODE_ENV === 'development' && (
        <div className="virtual-grid-debug">
          <small>
            Virtual Grid: {games.length} games, {rowCount} rows, {columnsPerRow} cols
          </small>
        </div>
      )}
    </div>
  );
});

VirtualGameGrid.displayName = "VirtualGameGrid";

/**
 * Placeholder LibraryGameCard component (to be replaced with actual implementation)
 */
const LibraryGameCard = memo<{
  game: LibraryGame;
  cardSize: LibraryCardSize;
  onAddToCollection?: (game: LibraryGame) => void;
  onRemoveFromCollection?: (game: LibraryGame) => void;
}>(({ game, cardSize, onAddToCollection, onRemoveFromCollection }) => {
  const handleAddToCollection = useCallback(() => {
    onAddToCollection?.(game);
  }, [game, onAddToCollection]);

  const handleRemoveFromCollection = useCallback(() => {
    onRemoveFromCollection?.(game);
  }, [game, onRemoveFromCollection]);

  return (
    <div className={`library-game-card library-game-card--${cardSize}`}>
      <div className="library-game-card__image">
        <img 
          src={game.coverImageUrl || '/placeholder-game.jpg'} 
          alt={game.title}
          loading="lazy"
        />
      </div>
      <div className="library-game-card__content">
        <h3 className="library-game-card__title">{game.title}</h3>
        <div className="library-game-card__actions">
          <button onClick={handleAddToCollection}>Add</button>
          <button onClick={handleRemoveFromCollection}>Remove</button>
        </div>
      </div>
    </div>
  );
});

LibraryGameCard.displayName = "LibraryGameCard";

/**
 * Hook for virtual scrolling performance monitoring
 */
export function useVirtualScrollPerformance() {
  const [metrics, setMetrics] = useState({
    renderTime: 0,
    visibleItems: 0,
    totalItems: 0,
  });

  const updateMetrics = useCallback((renderTime: number, visibleItems: number, totalItems: number) => {
    setMetrics({ renderTime, visibleItems, totalItems });
  }, []);

  return { metrics, updateMetrics };
}
