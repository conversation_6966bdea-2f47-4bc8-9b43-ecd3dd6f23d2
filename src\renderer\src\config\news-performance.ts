/**
 * Performance configuration for Steam News system
 */

export const NEWS_PERFORMANCE_CONFIG = {
  // Cache settings
  CACHE_DURATION: 30 * 60 * 1000, // 30 minutes
  STALE_TIME: 10 * 60 * 1000, // 10 minutes
  CACHE_TIME: 60 * 60 * 1000, // 1 hour

  // Request settings
  RATE_LIMIT_DELAY: 100, // 100ms between requests
  MAX_CONCURRENT_REQUESTS: 5, // Maximum concurrent requests
  REQUEST_TIMEOUT: 10000, // 10 seconds timeout

  // Data limits
  MAX_GAMES_TO_PROCESS: 40, // Maximum games to fetch news for
  MAX_ITEMS_PER_GAME: 3, // Maximum news items per game
  MAX_NEWS_ITEMS_TOTAL: 50, // Maximum total news items to display

  // UI settings
  REFRESH_INTERVAL: 20 * 60 * 1000, // 20 minutes auto-refresh
  INTERSECTION_THRESHOLD: 0.1, // 10% visibility to trigger loading
  INTERSECTION_ROOT_MARGIN: '50px', // Start loading 50px before visible

  // Image optimization
  IMAGE_LAZY_LOADING: true,
  IMAGE_FALLBACK_ENABLED: true,
  IMAGE_CACHE_DURATION: 24 * 60 * 60 * 1000, // 24 hours

  // Background processing
  BACKGROUND_REFRESH_ENABLED: true,
  BACKGROUND_REFRESH_DELAY: 5000, // 5 seconds delay for background refresh

  // Error handling
  MAX_RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 2000, // 2 seconds
  ERROR_CACHE_DURATION: 5 * 60 * 1000, // 5 minutes
} as const;

/**
 * Library Performance Configuration
 * Optimized settings for the library page performance
 */
export const LIBRARY_PERFORMANCE_CONFIG = {
  // Filtering and sorting
  DEBOUNCE_SEARCH_DELAY: 300, // 300ms debounce for search
  DEBOUNCE_FILTER_DELAY: 150, // 150ms debounce for filters
  MAX_GAMES_BEFORE_VIRTUALIZATION: 100, // Enable virtual scrolling after 100 games
  CHUNK_SIZE_FOR_PROCESSING: 50, // Process games in chunks of 50

  // Image loading
  IMAGE_LAZY_LOADING_THRESHOLD: 0.1, // 10% visibility to start loading
  IMAGE_PRELOAD_COUNT: 10, // Preload next 10 images
  IMAGE_CACHE_SIZE: 200, // Cache up to 200 images
  IMAGE_LOADING_TIMEOUT: 5000, // 5 seconds timeout for image loading

  // Virtual scrolling
  VIRTUAL_ITEM_HEIGHT: 280, // Height of each game card in grid mode
  VIRTUAL_OVERSCAN: 5, // Render 5 extra items outside viewport
  VIRTUAL_BUFFER_SIZE: 20, // Keep 20 items in buffer

  // Collection switching
  COLLECTION_SWITCH_DEBOUNCE: 100, // 100ms debounce for collection switching
  SKELETON_DISPLAY_MIN_TIME: 200, // Show skeleton for at least 200ms

  // Memory management
  MAX_CACHED_COLLECTIONS: 10, // Cache up to 10 collection results
  CLEANUP_INTERVAL: 5 * 60 * 1000, // Cleanup every 5 minutes

  // Performance budgets
  MAX_FILTER_TIME: 100, // Maximum time for filtering operation (ms)
  MAX_SORT_TIME: 50, // Maximum time for sorting operation (ms)
  MAX_RENDER_TIME: 16, // Maximum time for render cycle (ms)
} as const;

/**
 * Performance monitoring configuration
 */
export const PERFORMANCE_MONITORING = {
  ENABLE_LOGGING: process.env.NODE_ENV === 'development',
  LOG_CACHE_HITS: true,
  LOG_FETCH_TIMES: true,
  LOG_RENDER_TIMES: true,
  WARN_SLOW_OPERATIONS: true,
  SLOW_OPERATION_THRESHOLD: 1000, // 1 second
} as const;

/**
 * Feature flags for gradual rollout
 */
export const NEWS_FEATURE_FLAGS = {
  USE_OPTIMIZED_HOOK: true,
  USE_INTERSECTION_OBSERVER: true,
  USE_REQUEST_QUEUE: true,
  USE_BACKGROUND_REFRESH: true,
  USE_STALE_WHILE_REVALIDATE: true,
  USE_IMAGE_OPTIMIZATION: true,
} as const;

/**
 * Get performance config based on environment
 */
export function getPerformanceConfig() {
  const isDevelopment = process.env.NODE_ENV === 'development';
  const isProduction = process.env.NODE_ENV === 'production';
  
  return {
    ...NEWS_PERFORMANCE_CONFIG,
    // Adjust settings based on environment
    CACHE_DURATION: isDevelopment 
      ? 5 * 60 * 1000 // 5 minutes in dev
      : NEWS_PERFORMANCE_CONFIG.CACHE_DURATION,
    
    REFRESH_INTERVAL: isDevelopment
      ? 5 * 60 * 1000 // 5 minutes in dev
      : NEWS_PERFORMANCE_CONFIG.REFRESH_INTERVAL,
      
    MAX_GAMES_TO_PROCESS: isDevelopment
      ? 20 // Fewer games in dev
      : NEWS_PERFORMANCE_CONFIG.MAX_GAMES_TO_PROCESS,
  };
}

/**
 * Performance metrics tracking
 */
export class NewsPerformanceTracker {
  private static instance: NewsPerformanceTracker;
  private metrics: Map<string, number[]> = new Map();
  
  static getInstance(): NewsPerformanceTracker {
    if (!NewsPerformanceTracker.instance) {
      NewsPerformanceTracker.instance = new NewsPerformanceTracker();
    }
    return NewsPerformanceTracker.instance;
  }
  
  startTimer(operation: string): () => void {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      this.recordMetric(operation, duration);
      
      if (PERFORMANCE_MONITORING.ENABLE_LOGGING) {
        console.log(`⏱️ ${operation}: ${duration.toFixed(2)}ms`);
        
        if (PERFORMANCE_MONITORING.WARN_SLOW_OPERATIONS && 
            duration > PERFORMANCE_MONITORING.SLOW_OPERATION_THRESHOLD) {
          console.warn(`🐌 Slow operation detected: ${operation} took ${duration.toFixed(2)}ms`);
        }
      }
    };
  }
  
  private recordMetric(operation: string, duration: number): void {
    if (!this.metrics.has(operation)) {
      this.metrics.set(operation, []);
    }
    
    const metrics = this.metrics.get(operation)!;
    metrics.push(duration);
    
    // Keep only last 100 measurements
    if (metrics.length > 100) {
      metrics.shift();
    }
  }
  
  getAverageTime(operation: string): number {
    const metrics = this.metrics.get(operation);
    if (!metrics || metrics.length === 0) return 0;
    
    return metrics.reduce((sum, time) => sum + time, 0) / metrics.length;
  }
  
  getMetrics(): Record<string, { average: number; count: number; latest: number }> {
    const result: Record<string, { average: number; count: number; latest: number }> = {};
    
    for (const [operation, times] of this.metrics.entries()) {
      result[operation] = {
        average: this.getAverageTime(operation),
        count: times.length,
        latest: times[times.length - 1] || 0,
      };
    }
    
    return result;
  }
  
  clearMetrics(): void {
    this.metrics.clear();
  }
}

export const performanceTracker = NewsPerformanceTracker.getInstance();
